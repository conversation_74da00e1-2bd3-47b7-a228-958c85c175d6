import { useCallback, useEffect, useRef, useState } from "react";
import { flushSync } from "react-dom";
import { FormProvider, useForm } from "react-hook-form";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useAppCompleteApi } from "../../hooks/useAppCompleteApi.js";
import { useAppSignedApi } from "../../hooks/useAppSignedApi.js";
import { useAppStartApi } from "../../hooks/useAppStartApi.js";
import { useAppStorage } from "../../hooks/useAppStorage.js";
import { useEditAppApi } from "../../hooks/useEditAppApi.js";
import { useGetAppApi } from "../../hooks/useGetAppApi.js";
import { useSubmitAppApi } from "../../hooks/useSubmitAppApi.js";
import { trackCustomEvent, trackFieldFilled, trackStepCompleted } from "../../utils/analytics.js";
import { APP_FLOW_STATUS, defaultApplicationValues, isAppCompletedOrDocsPending } from "../../utils/consts.js";
import { logger } from "../../utils/logger.js";
import DataSecuredCard from "../shared/DataSecuredCard.jsx";
import { ErrorModal } from "../shared/ErrorModal.jsx";
import { LoadingOverlay } from "../shared/LoadingSpinner.jsx";
import { VerticalProgressBar } from "../shared/VerticalProgressBar.jsx";
import { ErrorState } from "./ErrorState.jsx";
import { SkeletonLoading } from "./SkeletonLoading.jsx";
import { BankStatements } from "./steps/BankStatements.jsx";
import { BusinessInfo } from "./steps/BusinessInfo.jsx";
import { OwnerInfo } from "./steps/OwnerInfo.jsx";
import { SignApplication } from "./steps/SignApplication.jsx";

// Define steps for the application form
const steps = [
  {
    id: "business",
    title: "Business details",
    description: "Provide key information about your business.",
  },
  {
    id: "owner",
    title: "Owner details",
    description: "Tell us about the business owner(s).",
  },
  {
    id: "sign",
    title: "Sign application",
    description: "Review and sign your application.",
  },
  {
    id: "bank",
    title: "Bank statements",
    description: "Upload your recent business bank statements.",
  },
];

/**
 * New Application Form component (Presentational)
 * This component is purely presentational and receives all necessary props from a container component
 *
 * @param {Object} props - Component props
 * @param {string} props.applicationId - Application ID
 * @param {number} props.currentStep - Current step index
 * @param {Object} props.formMethods - react-hook-form methods
 * @param {Function} props.onBlur - Function to call on blur
 * @param {string} props.formStatus - Form status (idle, submitting, success, error)
 * @param {Function} props.handlePrevStep - Function to handle previous step button click
 * @param {Function} props.handleNextStep - Function to handle next step button click
 * @param {Function} props.handleSubmit - Function to handle form submission
 * @param {boolean} props.showErrorModal - Whether to show error modal
 * @param {string} props.errorMessage - Error message to display
 * @param {string} props.errorId - Error ID to display
 * @param {Function} props.handleCloseErrorModal - Function to close error modal
 * @param {Function} props.onSuggestionClick - Function to handle suggestion clicks
 * @param {Object} props.applicationResult - Application result data containing preQualifyFields
 * @returns {JSX.Element}
 */

const fieldsForStep = {
  0: [
    "businessName",
    "entityType",
    "ein",
    "industry",
    "businessStartDate",
    "businessPhone",
    "businessEmail",
    "address.line1",
    "address.city",
    "address.state",
    "address.zip",
  ],
  1: [
    "owners[0].firstName",
    "owners[0].lastName",
    "owners[0].phone",
    "owners[0].email",
    "owners[0].dateOfBirth",
    "owners[0].ssn",
    "owners[0].ownershipPercentage",
    "owners[0].address.line1",
    "owners[0].address.city",
    "owners[0].address.state",
    "owners[0].address.zip",

    "owners[1].firstName",
    "owners[1].lastName",
    "owners[1].phone",
    "owners[1].email",
    "owners[1].dateOfBirth",
    "owners[1].ssn",
    "owners[1].ownershipPercentage",
    "owners[1].address.line1",
    "owners[1].address.city",
    "owners[1].address.state",
    "owners[1].address.zip",
  ],
  2: [], // No fields to validate for sign application step
  3: ["bankStatements"], // Validate bank statements upload
};

const useInitFormState = () => {
  const [isReady, setIsReady] = useState(false);
  const { uuid: urlAppId } = useParams();
  const {
    applicationId,
    setApplicationId,
    clearAllData,
    applicationForm: savedFields,
    setApplicationForm: storeFormFields,
    applicationResult,
    setApplicationResult,
    applicationStarted,
    setApplicationStarted,
  } = useAppStorage();

  if (urlAppId != applicationId && !isReady) {
    clearAllData();
    setApplicationId(urlAppId);
  }

  const {
    isLoading,
    isSuccess,
    result: fetchedResult,
    isError: isFetchError,
    error,
    errorId,
  } = useGetAppApi(urlAppId, true, null);

  const [formValues, setFormValues] = useState(savedFields);

  const [currentStep, setCurrentStep] = useState(savedFields.currentStep || 0);

  const isSavedFieldsEmpty = JSON.stringify(savedFields) == JSON.stringify(defaultApplicationValues);

  if (isSuccess && fetchedResult && !isReady) {
    setApplicationResult(fetchedResult);

    let formValuesToUse = { ...savedFields };
    if (isSavedFieldsEmpty) {
      const preQualifyFields = fetchedResult.preQualifyFields;

      formValuesToUse = {
        ...defaultApplicationValues,
        businessName: preQualifyFields.businessName || "",
        businessStartDate: preQualifyFields.businessStartDate || "",
        businessPhone: "",
        businessEmail: "",
        owners: [
          {
            ...defaultApplicationValues.owners[0],
            firstName: preQualifyFields.firstName || "",
            lastName: preQualifyFields.lastName || "",
            phone: preQualifyFields.phone || "",
            email: preQualifyFields.email || "",
          },
        ],
      };
    }

    if (fetchedResult.applicationFields) {
      const isSavedAddressEmpty =
        JSON.stringify(savedFields.address) == JSON.stringify(defaultApplicationValues.address);

      formValuesToUse = {
        ...fetchedResult.applicationFields,
        address: {
          ...fetchedResult.applicationFields.address,
          ...(isSavedAddressEmpty ? {} : savedFields.address),
        },
      };

      formValuesToUse.owners = [];

      const isSavedOwner1Empty =
        JSON.stringify(savedFields.owners[0]) == JSON.stringify(defaultApplicationValues.owners[0]);

      formValuesToUse.owners[0] = {
        ...fetchedResult.applicationFields.owners[0],
        ...(isSavedOwner1Empty ? {} : savedFields.owners[0]),
      };

      const defaultOwner1 = { ...defaultApplicationValues.owners[0] };
      defaultOwner1.ownershipPercentage = 0;
      const isSavedOwner2Empty = JSON.stringify(savedFields.owners[1]) == JSON.stringify(defaultOwner1);

      if (fetchedResult.applicationFields.owners[1] || savedFields.owners[1]) {
        formValuesToUse.owners[1] = {
          ...fetchedResult.applicationFields.owners[1],
          ...(isSavedOwner2Empty ? {} : savedFields.owners[1]),
        };
      }
    }

    formValuesToUse.currentStep = savedFields.currentStep <= 1 ? savedFields.currentStep : 0;

    if (fetchedResult.status == APP_FLOW_STATUS.APP_SUBMITTED) {
      formValuesToUse.currentStep = 2;
    } else if (fetchedResult.status == APP_FLOW_STATUS.APP_SIGNED) {
      formValuesToUse.currentStep = 3;
    }

    setCurrentStep(formValuesToUse.currentStep);
    setFormValues(formValuesToUse);

    setIsReady(true);
  }

  if (isFetchError && !isReady) {
    clearAllData();
    setIsReady(true);
  }

  return {
    appId: urlAppId || applicationId,
    isReady,
    isFetchError,
    isLoading,
    error,
    errorId,
    currentStep,
    setCurrentStep,
    formValues,
    savedFields,
    storeFormFields,
    applicationResult,
    setApplicationResult,
    applicationStarted,
    setApplicationStarted,
    isSavedFieldsEmpty,
  };
};

export const ApplicationFormContainer = () => {
  const navigate = useNavigate();
  const {
    isReady,
    appId,
    isFetchError,
    isLoading,
    error,
    errorId,
    formValues,
    savedFields,
    storeFormFields,
    applicationResult,
    setApplicationResult,
    applicationStarted,
    setApplicationStarted,
    currentStep,
    setCurrentStep: setStep,
    isSavedFieldsEmpty,
  } = useInitFormState();

  // Form state
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [isSigningComplete, setIsSigningComplete] = useState(false);
  const [isSignDocLoaded, setIsSignDocLoaded] = useState(false);

  // API hooks
  const { submitApp, isLoading: isSubmitting, error: submitError, errorId: submitErrorId } = useSubmitAppApi();

  const { editApp, isLoading: isEditing, error: editError, errorId: editErrorId } = useEditAppApi();

  const { completeApp, isLoading: isCompleting, error: completeError, errorId: completeErrorId } = useAppCompleteApi();

  const { appSigned } = useAppSignedApi();

  const { startApp } = useAppStartApi();

  // Handle closing the error modal
  const handleCloseErrorModal = () => {
    setShowErrorModal(false);
  };

  // Initialize form with react-hook-form
  const formMethods = useForm({
    defaultValues: formValues,
    mode: "onSubmit",
    reValidateMode: "onBlur",
    criteriaMode: "all",
  });

  const setCurrentStep = useCallback(
    (step) => {
      setStep(step);
      formMethods.setValue("currentStep", step);
      storeFormFields((prev) => ({
        ...prev,
        currentStep: step,
      }));
    },
    [setStep, formMethods, storeFormFields],
  );

  useEffect(() => {
    if (applicationResult?.status == APP_FLOW_STATUS.PREQUAL_DENIED) {
      navigate(`/prequalify-result/${appId}`);
    }

    if (isAppCompletedOrDocsPending(applicationResult)) {
      navigate(`/application/${appId}/result`);
    }
  }, [applicationResult, navigate, appId]);

  const initRef = useRef(false);
  useEffect(() => {
    if (isReady && !initRef.current) {
      formMethods.reset(formValues);

      initRef.current = true;

      trackCustomEvent("application_form_started", true);

      // Send app started event if not already sent
      if (!applicationStarted && applicationResult?.status.startsWith("PREQUAL")) {
        const sendAppStartEvent = async () => {
          try {
            await startApp(appId);
            setApplicationStarted(true);
          } catch (error) {
            logger.error("Error sending app start event:", error);
          }
        };
        sendAppStartEvent();
      }

      const startedAt = sessionStorage.getItem("application_form_started_at");
      if (!startedAt) {
        sessionStorage.setItem("application_form_started_at", Date.now());
      }

      Object.keys(formValues).forEach((key) => {
        const value = formValues[key];

        if (key == "owners") {
          value.forEach((owner, index) => {
            Object.keys(owner).forEach((ownerKey) => {
              if (!owner[ownerKey]) return;
              if (ownerKey == "address") {
                Object.keys(owner[ownerKey]).forEach((addressKey) => {
                  if (!owner[ownerKey][addressKey]) return;
                  trackFieldFilled(
                    `owners[${index}].address.${addressKey}`,
                    owner[ownerKey][addressKey],
                    "ApplicationForm",
                  );
                });
              } else {
                trackFieldFilled(`owners[${index}].${ownerKey}`, owner[ownerKey], "ApplicationForm");
              }
            });
          });
        } else if (key == "address") {
          Object.keys(value).forEach((addressKey) => {
            if (!value[addressKey]) return;
            trackFieldFilled(`address.${addressKey}`, value[addressKey], "ApplicationForm");
          });
        } else if (key == "bankStatements") {
          if (value && value.length > 0) {
            trackFieldFilled("bankStatements", `${value.length} uploaded`, "ApplicationForm");
          }
        } else {
          trackFieldFilled(key, formValues[key], "ApplicationForm");
        }
      });
    }
  }, [
    isReady,
    formValues,
    formMethods,
    isSavedFieldsEmpty,
    appId,
    applicationStarted,
    setApplicationStarted,
    startApp,
    applicationResult?.status,
  ]);

  // Handle suggestion click
  const onSuggestionClick = (name, value) => {
    formMethods.setValue(name, value, { shouldValidate: true });
    trackFieldFilled(name, value, "ApplicationForm");
    storeFormFields(formMethods.getValues());
  };

  // Handle previous step button click
  const handlePrevStep = async () => {
    // If we're on the sign application step (step 2), we need to call the edit API
    if (currentStep === 2) {
      try {
        // Call the edit API
        await editApp(appId);

        // Update application result status
        if (applicationResult) {
          setApplicationResult({
            ...applicationResult,
            currentStep: 0,
            status: APP_FLOW_STATUS.APP_EDITING,
          });
        }
        setCurrentStep(0);
      } catch (error) {
        logger.error("Error editing application:", error);
        // Show error modal
        setShowErrorModal(true);
        return; // Don't proceed if there was an error
      }
      const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
      scrollTarget.scrollIntoView({ behavior: "smooth" });
      return;
    }

    const newStep = Math.max(0, currentStep - 1);
    setCurrentStep(newStep);

    const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
    scrollTarget.scrollIntoView({ behavior: "smooth" });
  };

  // Handle initial application submission (at step 1)
  const handleInitialSubmit = async () => {
    const [isStep0Valid, isStep1Valid] = await Promise.all([
      formMethods.trigger(fieldsForStep[0]),
      formMethods.trigger(fieldsForStep[1]),
    ]);

    if (!isStep0Valid) {
      setCurrentStep(0);
      return;
    } else if (!isStep1Valid) {
      return;
    }

    // Check if there are multiple owners and validate total ownership percentage
    const data = formMethods.getValues();
    if (data.owners && data.owners.length > 1 && data.owners[1]) {
      const owner1Percentage = parseInt(data.owners[0]?.ownershipPercentage) || 0;
      const owner2Percentage = parseInt(data.owners[1]?.ownershipPercentage) || 0;
      const totalPercentage = owner1Percentage + owner2Percentage;

      if (totalPercentage > 100) {
        // Set error for the second owner's percentage field
        formMethods.setError("owners[1].ownershipPercentage", {
          type: "manual",
          message: `Combined ownership percentage (${totalPercentage}%) cannot exceed 100%`,
        });

        const element = document.getElementsByName("owners[1].ownershipPercentage")[0];
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "center" });
          setTimeout(() => {
            element.focus();
          }, 500);
        }
        return;
      }
    } else {
      data.owners = [data.owners[0]];
    }

    const formElement = document.querySelector("form");
    if (formElement) {
      formElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    try {
      // Track application submission
      data.owners[0].ownershipPercentage = Number(data.owners[0].ownershipPercentage) || 0;
      if (data.owners[1]) {
        data.owners[1].ownershipPercentage = Number(data.owners[1].ownershipPercentage) || 0;
      }

      sessionStorage.setItem("application_form_submitted_at", Date.now());
      const result = await submitApp(appId, data);
      if (result && result.data) {
        setApplicationResult(result.data);
      }

      const newStep = 2; // Sign application step
      trackStepCompleted("Form Step Completed: Owner Info", "Application");
      setCurrentStep(newStep);
      setIsSignDocLoaded(false);

      // Scroll to the top of the form
      const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
      scrollTarget.scrollIntoView({ behavior: "smooth" });
    } catch (error) {
      logger.error("Error submitting application:", error);
      // Error state is handled by the hook

      // Show error in UI
      setShowErrorModal(true);
    }
  };

  const handleDocumentLoaded = () => {
    setIsSignDocLoaded(true);
  };

  const handleDocumentSigned = async () => {
    try {
      await appSigned(appId);
      const newStep = 3;

      setIsSigningComplete(true);

      // Delay the step change for 250ms
      setTimeout(() => {
        flushSync(() => {
          // Update application result status
          setApplicationResult({
            ...applicationResult,
            status: APP_FLOW_STATUS.APP_SIGNED,
          });
          setCurrentStep(newStep);
          trackStepCompleted("Form Step Completed: Sign Application", "Application");

          setIsSigningComplete(false); // Reset the loading state
        });

        const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
        scrollTarget.scrollIntoView({ behavior: "smooth" });
      }, 250);
    } catch (error) {
      logger.error("Error updating signed status:", error);
      // Error state is handled by the hook
      setShowErrorModal(true);
      return;
    }
  };

  // Handle next step button click
  const handleNextStep = async () => {
    // Determine which fields to validate based on current step

    if (currentStep === 2) return;

    const isValid = await formMethods.trigger(fieldsForStep[currentStep]);

    const errors = formMethods.formState.errors;

    for (const field of fieldsForStep[currentStep]) {
      const val = getValueByPath(errors, field);
      if (val) {
        const element = document.getElementsByName(field)[0];
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "center" });
          setTimeout(() => {
            element.focus();
          }, 500);
        }
        break;
      }
    }

    if (!isValid) {
      return;
    }

    switch (currentStep) {
      case 0: {
        const newStep = currentStep + 1;
        setCurrentStep(newStep);
        trackStepCompleted("Form Step Completed: Business Info", "Application");
        // Scroll to the top of the form
        const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
        scrollTarget.scrollIntoView({ behavior: "smooth" });
        break;
      }
      case 1:
        handleInitialSubmit();
        break;
      case 2:
        // handled by handleDocumentSigned
        break;
      case 3:
        handleSubmit(formMethods.getValues());
        break;
    }
  };

  // Handle skipping the bank statements step
  const handleSkipBankStatements = async () => {
    // Set an empty array for bank statements
    formMethods.setValue("bankStatements", [], { shouldValidate: false });

    // Track that the user skipped this step
    trackFieldFilled("bankStatements", "skipped", "ApplicationForm");

    // Track the new custom event for skipped bank statements
    trackCustomEvent("skipped_bank_statements", true);

    // Submit the form with empty bank statements
    handleSubmitWithoutValidation();
  };

  // Handle final form submission (at step 3)
  const handleSubmit = async () => {
    // We only need to validate the bank statements at this point
    // since the application was already submitted at step 1
    // and the document was signed at step 2

    let isValid = await formMethods.trigger(fieldsForStep[3]);

    // let data = formMethods.getValues();
    // Check if bank statements have been uploaded (allow empty array for skip option)
    // if (!data.bankStatements || data.bankStatements.length !== 3) {
    //   isValid = false;
    //   formMethods.setError("bankStatements", {
    //     type: "manual",
    //     message: "Please upload 3-6 bank statements or skip this step",
    //   });
    // }

    if (!isValid) {
      // Just focus on the first invalid field without showing error modal
      const errors = formMethods.formState.errors;

      // Focus on the first invalid field
      const firstError = Object.keys(errors)[0];
      if (firstError) {
        const element = document.getElementsByName(firstError)[0];
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "center" });
          setTimeout(() => {
            element.focus();
          }, 500);
        }
      }
      return;
    }

    trackCustomEvent("uploaded_bank_statements", true);

    // If validation passes, proceed with submission
    handleSubmitWithoutValidation();
  };

  // Handle submission without validation (for skip option)
  const handleSubmitWithoutValidation = async () => {
    // Get form data
    const data = formMethods.getValues();

    data.owners[0].ownershipPercentage = Number(data.owners[0].ownershipPercentage) || 0;

    if (data.owners[1]) {
      data.owners[1].ownershipPercentage = Number(data.owners[1].ownershipPercentage) || 0;
    } else {
      data.owners = [data.owners[0]];
    }

    // Center the form to show the loading overlay
    const formElement = document.querySelector("form");
    if (formElement) {
      formElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    try {
      // 1. Convert the File objects to FormData or base64 strings if needed
      // 2. Send the data to the server using our completeApp hook

      // Call the API to complete the application with bank statements
      const result = await completeApp(appId, {
        bankStatements: data.bankStatements,
      });

      // Save the entire API response in cookie storage
      if (result && result.data) {
        trackStepCompleted("Form Step Completed: Bank Statements", "Application");
        setApplicationResult(result.data);

        // Track application completion

        // Redirect to the result page
        navigate(`/application/${appId}/result`);
        window.scrollTo({ top: 0, behavior: "instant" });
      } else {
        throw new Error("Unexpected API response format");
      }
    } catch (error) {
      logger.error("Error completing application:", error);
      // Error state is handled by the hook
      setShowErrorModal(true);
    }
  };

  const handleOnBlur = async (e) => {
    const fieldName = e.target.name;
    if (!fieldName) return;

    const newValues = formMethods.getValues();
    const fieldsToUpdate = [fieldName];

    const activeStepFields = fieldsForStep[currentStep];

    newValues.owners[0].ownershipPercentage = Number(newValues.owners[0].ownershipPercentage);
    if (newValues.owners[1]) {
      newValues.owners[1].ownershipPercentage = Number(newValues.owners[1].ownershipPercentage);
    }
    storeFormFields({ ...newValues });

    if (currentStep === 0) {
      for (const field of activeStepFields) {
        if (field !== fieldName && savedFields[field] !== newValues[field]) {
          fieldsToUpdate.push(field);
        }
      }
    } else {
      if (
        fieldName &&
        (fieldName === "owners[0].ownershipPercentage" || fieldName === "owners[1].ownershipPercentage")
      ) {
        if (validateOwnershipPercentage(newValues, formMethods)) {
          if (formValues.owners && formValues.owners.length > 1) {
            trackFieldFilled(
              "owners[0].ownershipPercentage",
              formMethods.getValues("owners[0].ownershipPercentage"),
              "ApplicationForm",
            );
            trackFieldFilled(
              "owners[1].ownershipPercentage",
              formMethods.getValues("owners[1].ownershipPercentage"),
              "ApplicationForm",
            );
          } else {
            trackFieldFilled(fieldName, formMethods.getValues(fieldName), "ApplicationForm");
          }
        } else {
          trackCustomEvent("form_field_error", fieldName, false);
          return;
        }
      }

      for (const field of activeStepFields) {
        const state = formMethods.getFieldState(field);
        if (state.isDirty) {
          fieldsToUpdate.push(field);
        }
      }
    }

    const isValid = await formMethods.trigger(fieldsToUpdate);

    // if the current field has error, track event
    const fieldHasError = formMethods.formState.errors[fieldName];
    if (fieldHasError) {
      trackCustomEvent("form_field_error", fieldName, false);
    }

    if (isValid) {
      fieldsToUpdate.forEach((field) => {
        trackFieldFilled(field, formMethods.getValues(field), "ApplicationForm");
      });
    }
  };

  if (isFetchError) {
    return <ErrorState message={error || "An error occurred"} errorId={errorId || "UNKNOWN_ERROR"} />;
  }

  // Show loading skeleton while fetching data
  if (isLoading || !isReady) {
    return <SkeletonLoading />;
  }

  return (
    <div className="container mx-auto p-4">
      <ApplicationFormUI
        applicationId={appId}
        currentStep={currentStep}
        formMethods={formMethods}
        onBlur={handleOnBlur}
        isSubmitting={isSubmitting || isCompleting}
        isEditing={isEditing}
        isSignDocLoaded={isSignDocLoaded}
        isSigningComplete={isSigningComplete}
        handlePrevStep={handlePrevStep}
        handleNextStep={handleNextStep}
        handleSubmit={handleSubmit}
        handleSkipBankStatements={handleSkipBankStatements}
        handleDocumentSigned={handleDocumentSigned}
        handleDocumentLoaded={handleDocumentLoaded}
        appSigned={applicationResult?.status == APP_FLOW_STATUS.APP_SIGNED}
        showErrorModal={isFetchError || showErrorModal}
        errorMessage={
          submitError || editError || completeError || error?.message || "An error occurred. Please try again."
        }
        errorId={submitErrorId || editErrorId || completeErrorId || error?.id || "UNKNOWN_ERROR"}
        handleCloseErrorModal={handleCloseErrorModal}
        onSuggestionClick={onSuggestionClick}
        applicationResult={applicationResult}
      />
    </div>
  );
};

export const ApplicationFormUI = ({
  applicationId,
  currentStep = 0,
  formMethods,
  onBlur,
  isSubmitting = false,
  isEditing = false,
  isSignDocLoaded = false,
  isSigningComplete = false,
  appSigned,
  handlePrevStep,
  handleNextStep,
  handleSubmit,
  handleSkipBankStatements,
  handleDocumentSigned,
  handleDocumentLoaded,
  showErrorModal = false,
  errorMessage = "",
  errorId = "",
  handleCloseErrorModal,
  onSuggestionClick,
  applicationResult,
}) => {
  const scrolledOnce = useRef(false);
  const [params, setParams] = useSearchParams();
  const fromFastTrack = params.get("fromFastTrack") === "true";

  // Track if user has selected upload or skip in BankStatements
  const [bankReadyToSubmit, setBankReadyToSubmit] = useState(false);

  useEffect(() => {
    if (scrolledOnce.current || !fromFastTrack) return;

    const scrollTarget = document.getElementById("application-form-scroll-target") || document.forms[0];
    scrollTarget.scrollIntoView({ behavior: "smooth" });
    scrolledOnce.current = true;
    // remove fromFastTrack param from URL
    const newParams = new URLSearchParams(params);
    newParams.delete("fromFastTrack");
    setParams(newParams);
  }, [fromFastTrack, params, setParams]);

  // No longer using PandaDoc sessionId

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex flex-col md:flex-row">
        {/* Vertical Progress Bar - Outside the form card */}
        <div className="md:w-1/3 mb-6 md:mb-0 md:pr-8">
          <VerticalProgressBar steps={steps} currentStep={currentStep} className="sticky top-6" />
        </div>

        {/* Form Content */}
        <div className="md:w-2/3">
          <div id="application-form-scroll-target" />
          <div className="bg-white rounded-sm shadow-lg p-6 relative">
            <h2 className="text-4xl font-bold text-center text-gray-800 mb-6">Complete Your Application</h2>
            {/* Loading Overlay */}
            <LoadingOverlay
              isLoading={isSubmitting || isEditing || isSigningComplete}
              message={
                isSigningComplete
                  ? "Application Signed"
                  : isEditing
                    ? "Retrieving application..."
                    : currentStep === 1
                      ? "Almost there — preparing your document for signing."
                      : currentStep === 3
                        ? "Completing your application..."
                        : "Submitting your application..."
              }
            />

            <FormProvider {...formMethods}>
              <form onSubmit={formMethods.handleSubmit(handleSubmit)} onBlur={onBlur}>
                {/* Render all step components but only show the current one */}
                <div style={{ display: currentStep === 0 ? "block" : "none" }}>
                  <BusinessInfo
                    control={formMethods.control}
                    onSuggestionClick={onSuggestionClick}
                    preQualifyResult={applicationResult?.preQualifyFields}
                  />
                </div>
                <div style={{ display: currentStep === 1 ? "block" : "none" }}>
                  <OwnerInfo control={formMethods.control} onSuggestionClick={onSuggestionClick} />
                </div>
                <div style={{ display: currentStep === 2 ? "block" : "none" }}>
                  {currentStep === 2 && (
                    <SignApplication
                      appId={applicationId}
                      control={formMethods.control}
                      onDocumentSigned={handleDocumentSigned}
                      onDocumentLoaded={handleDocumentLoaded}
                      onRevisionsClick={handlePrevStep}
                    />
                  )}
                </div>
                <div style={{ display: currentStep === 3 ? "block" : "none" }}>
                  <BankStatements
                    control={formMethods.control}
                    onSkip={handleSkipBankStatements}
                    onReadyToSubmit={setBankReadyToSubmit}
                  />
                </div>

                <div className="flex justify-between mt-6 pt-4 border-t border-gray-200">
                  {currentStep > 0 && currentStep !== 2 && !appSigned ? (
                    <button
                      data-hj-allow
                      type="button"
                      onClick={handlePrevStep}
                      className="px-6 py-2 bg-gray-200 text-gray-800 rounded-sm hover:bg-gray-300 transition-colors"
                      disabled={currentStep === 2 ? !isSignDocLoaded : isSubmitting}
                    >
                      {" "}
                      Previous
                    </button>
                  ) : (
                    <div></div> // Empty div to maintain flex spacing
                  )}

                  {currentStep < steps.length - 1 && currentStep !== 2 && (
                    <div className="flex items-center ml-auto">
                      {/* Document signed indicator - only show when document is signed and matches current session */}
                      {currentStep === 2 && appSigned && <DocumentSignedIndicator />}
                      <button
                        data-hj-allow
                        type="button"
                        onClick={handleNextStep}
                        className="px-6 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-700 transition-colors"
                        disabled={isSubmitting}
                      >
                        {currentStep === 0
                          ? "Continue"
                          : currentStep === 1 // Submit Application → Continue for user engagement
                            ? "Continue"
                            : "Continue"}
                      </button>
                    </div>
                  )}
                  {currentStep === 3 && bankReadyToSubmit && (
                    <button
                      data-hj-allow
                      type="button"
                      onClick={handleSubmit}
                      className="px-6 py-2 bg-blue-600 text-white rounded-sm hover:bg-blue-700 transition-colors"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Submitting..." : "Submit"}
                    </button>
                  )}
                </div>
              </form>
            </FormProvider>
          </div>

          {/* Data Secured Card */}
          <DataSecuredCard />
        </div>
      </div>

      {/* Error Modal */}
      <ErrorModal isOpen={showErrorModal} onClose={handleCloseErrorModal} error={errorMessage} errorId={errorId} />
    </div>
  );
};

const DocumentSignedIndicator = () => {
  return (
    <div className="flex items-center text-green-600 mr-4">
      <svg className="h-5 w-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path
          fillRule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clipRule="evenodd"
        />
      </svg>
      <span className="font-medium text-sm">Document Signed</span>
    </div>
  );
};

function validateOwnershipPercentage(formValues, formMethods) {
  // Check if there are multiple owners
  let isValid = true;
  if (formValues.owners && formValues.owners.length > 1 && formValues.owners[1]) {
    const owner1Percentage = parseInt(formValues.owners[0]?.ownershipPercentage) || 0;
    const owner2Percentage = parseInt(formValues.owners[1]?.ownershipPercentage) || 0;
    const totalPercentage = owner1Percentage + owner2Percentage;

    formValues.owners[0].ownershipPercentage = Number(formValues.owners[0].ownershipPercentage);
    formValues.owners[1].ownershipPercentage = Number(formValues.owners[1].ownershipPercentage);

    if (totalPercentage > 100) {
      // Set error for the second owner's percentage field
      isValid = false;
      formMethods.setError("owners[1].ownershipPercentage", {
        type: "manual",
        message: `Combined ownership percentage (${totalPercentage}%) cannot exceed 100%`,
      });
    } else {
      // Clear the error if the total is now valid
      formMethods.clearErrors("owners[1].ownershipPercentage");
    }
  } else {
    formValues.owners[0].ownershipPercentage = Number(formValues.owners[0].ownershipPercentage);
  }
  return isValid;
}

function getValueByPath(obj, path) {
  const pathParts = path
    .replace(/\[(\d+)]/g, ".$1") // Convert [0] to .0
    .split("."); // Split into keys

  return pathParts.reduce((acc, key) => acc?.[key], obj);
}
