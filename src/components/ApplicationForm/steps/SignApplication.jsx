import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { useAppStorage } from "../../../hooks/useAppStorage";
import appLogo from "../../../assets/app-logo.svg";

/**
 * Sign Application step component
 * This component displays a custom HTML document template for signing
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onDocumentSigned - Function to call when document is signed
 * @returns {JSX.Element}
 */
export const SignApplication = ({ onDocumentSigned, onRevisionsClick, appId }) => {
  const [error, setError] = useState(null);
  const [signature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // "type" or "sign"
  const [isProcessingSignature, setIsProcessingSignature] = useState(false);

  const { watch } = useFormContext();
  const formData = watch();
  const { preQualifyResult } = useAppStorage();

  // Get current date for signature
  const currentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Handle signature submission
  const handleSignDocument = async () => {
    if (signatureMethod === "type" && !signature.trim()) {
      setError("Please enter your signature to continue.");
      return;
    }
    if (signatureMethod === "sign") {
      setError("Signature canvas functionality will be implemented later.");
      return;
    }

    setIsProcessingSignature(true);
    setError(null);

    try {
      // Simulate document signing process
      await new Promise((resolve) => setTimeout(resolve, 1000));
      onDocumentSigned();
    } catch {
      setError("There was a problem signing the document. Please try again.");
      setIsProcessingSignature(false);
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return "$0";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format address
  const formatAddress = (address) => {
    if (!address) return "";
    const parts = [address.line1, address.line2, address.city, address.state, address.zip].filter(Boolean);
    return parts.join(", ");
  };

  // Get funding information
  const fundingAmount = preQualifyResult?.preQualifyFields?.fundingAmount || formData?.fundingAmount;
  const purpose = preQualifyResult?.preQualifyFields?.purpose || formData?.purpose;

  // Get business information
  const businessInfo = {
    name: formData?.businessName || "",
    dbaName: formData?.dbaName || "",
    website: formData?.website || "",
    entityType: formData?.entityType || "",
    ein: formData?.ein || "",
    industry: formData?.industry || "",
    startDate: formData?.businessStartDate || "",
    phone: formData?.businessPhone || "",
    email: formData?.businessEmail || "",
    address: formatAddress(formData?.address),
  };

  // Get owner information
  const owner1 = formData?.owners?.[0] || {};
  const owner2 = formData?.owners?.[1] || null;

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Sign Your Application</h3>

      <p className="text-gray-600 mb-6">
        Please review and sign your application. For revisions{" "}
        <button onClick={onRevisionsClick} className="text-blue-600 hover:underline cursor-pointer" type="button">
          click here
        </button>
        .
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Custom Document Template */}
      <div className="bg-white border border-gray-300 rounded-sm shadow-lg">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex justify-between gap-4">
            {/* Logo */}
            <div className="flex-shrink-0">
              <img src={appLogo} alt="Pinnacle Funding" className="h-12 w-auto" />
            </div>

            {/* Contact Information */}
            <div className="text-left">
              <div className="grid grid-cols-[auto_1fr] gap-x-2 gap-y-1 text-sm max-w-md">
                <span className="font-medium text-gray-600">Phone:</span>
                <span className="font-semibold">(*************</span>

                <span className="font-medium text-gray-600">Email:</span>
                <span className="font-semibold"><EMAIL></span>

                <span className="font-medium text-gray-600">Website:</span>
                <a
                  href="https://pinnaclefundingco.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold hover:underline"
                >
                  PinnacleFundingCo.com
                </a>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-4 text-xs text-gray-600">
            There are no fees, charges, or obligations associated with obtaining a pre-approval. Pre-approval does not
            constitute a funding commitment.
          </div>
        </div>

        {/* Body */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Funding Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Funding Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Requested Amount:</span>{" "}
                  <span className="font-semibold text-gray-900">{formatCurrency(fundingAmount)}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Purpose:</span>{" "}
                  <span className="font-semibold text-gray-900">{purpose || "Not specified"}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Top Priority:</span>{" "}
                  <span className="font-semibold text-gray-900">
                    {preQualifyResult?.topPriority || "Not specified"}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Timeline:</span>{" "}
                  <span className="font-semibold text-gray-900">{preQualifyResult?.timeline || "Not specified"}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Monthly Revenue:</span>{" "}
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(preQualifyResult?.monthlyRevenue)}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Estimated FICO:</span>{" "}
                  <span className="font-semibold text-gray-900">
                    {preQualifyResult?.estimatedFICO || "Not specified"}
                  </span>
                </div>
              </div>
            </div>

            {/* Business Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Business Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Business Name:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.name}</span>
                </div>
                {businessInfo.dbaName && (
                  <div>
                    <span className="font-medium text-gray-600">DBA:</span>{" "}
                    <span className="font-semibold text-gray-900">{businessInfo.dbaName}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-gray-600">Entity Type:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.entityType}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">EIN:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.ein}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Industry:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.industry}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Start Date:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.startDate}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Phone:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.phone}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Email:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.email}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Address:</span>{" "}
                  <span className="font-semibold text-gray-900">{businessInfo.address}</span>
                </div>
              </div>
            </div>

            {/* Owner 1 Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Owner Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>{" "}
                  <span className="font-semibold text-gray-900">
                    {owner1.firstName} {owner1.lastName}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Date of Birth:</span>{" "}
                  <span className="font-semibold text-gray-900">{owner1.dateOfBirth}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Phone:</span>{" "}
                  <span className="font-semibold text-gray-900">{owner1.phone}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Email:</span>{" "}
                  <span className="font-semibold text-gray-900">{owner1.email}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Ownership:</span>{" "}
                  <span className="font-semibold text-gray-900">{owner1.ownershipPercentage}%</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Address:</span>{" "}
                  <span className="font-semibold text-gray-900">{formatAddress(owner1.address)}</span>
                </div>
              </div>
            </div>

            {/* Owner 2 Information (if exists) */}
            {owner2 && (
              <div className="bg-gray-50 p-4 rounded-sm">
                <h4 className="text-lg font-semibold mb-3 text-gray-800">Owner 2 Information</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium text-gray-600">Name:</span>{" "}
                    <span className="font-semibold text-gray-900">
                      {owner2.firstName} {owner2.lastName}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Date of Birth:</span>{" "}
                    <span className="font-semibold text-gray-900">{owner2.dateOfBirth}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Phone:</span>{" "}
                    <span className="font-semibold text-gray-900">{owner2.phone}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Email:</span>{" "}
                    <span className="font-semibold text-gray-900">{owner2.email}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Ownership:</span>{" "}
                    <span className="font-semibold text-gray-900">{owner2.ownershipPercentage}%</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Address:</span>{" "}
                    <span className="font-semibold text-gray-900">{formatAddress(owner2.address)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="space-y-4">
            {/* Disclaimer */}
            <div className="text-sm text-gray-700">
              <p className="mb-4">
                By signing below, I acknowledge that I have read and understand the terms and conditions of this funding
                application. I certify that all information provided is true and accurate to the best of my knowledge. I
                understand that this application does not guarantee funding approval and that additional documentation
                may be required.
              </p>
            </div>

            {/* Signature Method Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Signature Method</label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="type"
                    checked={signatureMethod === "type"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Type Signature
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="sign"
                    checked={signatureMethod === "sign"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Draw Signature
                </label>
              </div>
            </div>

            {/* Signature Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="signature" className="block text-sm font-medium text-gray-700 mb-1">
                  {signatureMethod === "type" ? "Type Your Signature" : "Draw Your Signature"}
                </label>
                <div className="h-12">
                  {signatureMethod === "type" ? (
                    <input
                      id="signature"
                      name="signature"
                      type="text"
                      placeholder="Type your full name"
                      value={signature}
                      onChange={(e) => setSignature(e.target.value)}
                      className="w-full h-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  ) : (
                    <div className="w-full h-full border border-gray-300 rounded text-sm bg-gray-50 flex items-center justify-center text-gray-500">
                      Signature canvas will be implemented here
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <div className="p-2 bg-white border border-gray-300 rounded text-sm">{currentDate}</div>
              </div>
            </div>

            {/* Application UUID */}
            <div className="text-xs text-gray-500 mt-4">Application UUID: {appId}</div>

            {/* Sign Button */}
            <div className="flex justify-center mt-6">
              <button
                onClick={handleSignDocument}
                disabled={isProcessingSignature || (signatureMethod === "type" && !signature.trim())}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-8 rounded-sm focus:outline-none focus:shadow-outline"
              >
                {isProcessingSignature ? "Processing..." : "Sign Application"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
