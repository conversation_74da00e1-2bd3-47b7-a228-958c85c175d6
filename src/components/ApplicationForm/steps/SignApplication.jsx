import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { useAppStorage } from "../../../hooks/useAppStorage";
import appLogo from "../../../assets/app-logo.svg";

/**
 * Sign Application step component
 * This component displays a custom HTML document template for signing
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onDocumentSigned - Function to call when document is signed
 * @returns {JSX.Element}
 */
export const SignApplication = ({ onDocumentSigned, onRevisionsClick, appId }) => {
  const [error, setError] = useState(null);
  const [signature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // "type" or "sign"
  const [isProcessingSignature, setIsProcessingSignature] = useState(false);

  const { watch } = useFormContext();
  const formData = watch();
  const { preQualifyResult } = useAppStorage();

  // Get current date for signature
  const currentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Handle signature submission
  const handleSignDocument = async () => {
    if (signatureMethod === "type" && !signature.trim()) {
      setError("Please enter your signature to continue.");
      return;
    }
    if (signatureMethod === "sign") {
      setError("Signature canvas functionality will be implemented later.");
      return;
    }

    setIsProcessingSignature(true);
    setError(null);

    try {
      // Simulate document signing process
      await new Promise((resolve) => setTimeout(resolve, 1000));
      onDocumentSigned();
    } catch {
      setError("There was a problem signing the document. Please try again.");
      setIsProcessingSignature(false);
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount) return "$0";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format address
  const formatAddress = (address) => {
    if (!address) return "";
    const parts = [address.line1, address.line2, address.city, address.state, address.zip].filter(Boolean);
    return parts.join(", ");
  };

  // Get funding information
  const fundingAmount = preQualifyResult?.preQualifyFields?.fundingAmount || formData?.fundingAmount;
  const purpose = preQualifyResult?.preQualifyFields?.purpose || formData?.purpose;

  // Get business information
  const businessInfo = {
    name: formData?.businessName || "",
    dbaName: formData?.dbaName || "",
    website: formData?.website || "",
    entityType: formData?.entityType || "",
    ein: formData?.ein || "",
    industry: formData?.industry || "",
    startDate: formData?.businessStartDate || "",
    phone: formData?.businessPhone || "",
    email: formData?.businessEmail || "",
    address: formatAddress(formData?.address),
  };

  // Get owner information
  const owner1 = formData?.owners?.[0] || {};
  const owner2 = formData?.owners?.[1] || null;

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Sign Your Application</h3>

      <p className="text-gray-600 mb-6">
        Please review and sign your application. For revisions{" "}
        <button onClick={onRevisionsClick} className="text-blue-600 hover:underline cursor-pointer" type="button">
          click here
        </button>
        .
      </p>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {/* Custom Document Template */}
      <div className="bg-white border border-gray-300 rounded-sm shadow-lg">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex justify-between gap-4">
            {/* Logo */}
            <div className="flex-shrink-0">
              <img src={appLogo} alt="Pinnacle Funding" className="h-12 w-auto" />
            </div>

            {/* Contact Information */}
            <div className="text-left">
              <div className="grid grid-cols-[auto_1fr] gap-x-2 gap-y-1 text-sm max-w-md">
                <span className="font-medium text-gray-600">Phone:</span>
                <span className="font-semibold">(*************</span>

                <span className="font-medium text-gray-600">Email:</span>
                <span className="font-semibold"><EMAIL></span>

                <span className="font-medium text-gray-600">Website:</span>
                <a
                  href="https://pinnaclefundingco.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold hover:underline"
                >
                  PinnacleFundingCo.com
                </a>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-4 text-xs text-gray-600">
            There are no fees, charges, or obligations associated with obtaining a pre-approval. Pre-approval does not
            constitute a funding commitment.
          </div>
        </div>

        {/* Body */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Funding Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Funding Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Requested Amount:</span> {formatCurrency(fundingAmount)}
                </div>
                <div>
                  <span className="font-medium">Purpose:</span> {purpose || "Not specified"}
                </div>
                <div>
                  <span className="font-medium">Top Priority:</span> {preQualifyResult?.topPriority || "Not specified"}
                </div>
                <div>
                  <span className="font-medium">Timeline:</span> {preQualifyResult?.timeline || "Not specified"}
                </div>
                <div>
                  <span className="font-medium">Monthly Revenue:</span>{" "}
                  {formatCurrency(preQualifyResult?.monthlyRevenue)}
                </div>
                <div>
                  <span className="font-medium">Estimated FICO:</span>{" "}
                  {preQualifyResult?.estimatedFICO || "Not specified"}
                </div>
              </div>
            </div>

            {/* Business Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Business Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Business Name:</span> {businessInfo.name}
                </div>
                {businessInfo.dbaName && (
                  <div>
                    <span className="font-medium">DBA:</span> {businessInfo.dbaName}
                  </div>
                )}
                <div>
                  <span className="font-medium">Entity Type:</span> {businessInfo.entityType}
                </div>
                <div>
                  <span className="font-medium">EIN:</span> {businessInfo.ein}
                </div>
                <div>
                  <span className="font-medium">Industry:</span> {businessInfo.industry}
                </div>
                <div>
                  <span className="font-medium">Start Date:</span> {businessInfo.startDate}
                </div>
                <div>
                  <span className="font-medium">Phone:</span> {businessInfo.phone}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {businessInfo.email}
                </div>
                <div>
                  <span className="font-medium">Address:</span> {businessInfo.address}
                </div>
              </div>
            </div>

            {/* Owner 1 Information */}
            <div className="bg-gray-50 p-4 rounded-sm">
              <h4 className="text-lg font-semibold mb-3 text-gray-800">Owner Information</h4>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Name:</span> {owner1.firstName} {owner1.lastName}
                </div>
                <div>
                  <span className="font-medium">Date of Birth:</span> {owner1.dateOfBirth}
                </div>
                <div>
                  <span className="font-medium">Phone:</span> {owner1.phone}
                </div>
                <div>
                  <span className="font-medium">Email:</span> {owner1.email}
                </div>
                <div>
                  <span className="font-medium">Ownership:</span> {owner1.ownershipPercentage}%
                </div>
                <div>
                  <span className="font-medium">Address:</span> {formatAddress(owner1.address)}
                </div>
              </div>
            </div>

            {/* Owner 2 Information (if exists) */}
            {owner2 && (
              <div className="bg-gray-50 p-4 rounded-sm">
                <h4 className="text-lg font-semibold mb-3 text-gray-800">Owner 2 Information</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Name:</span> {owner2.firstName} {owner2.lastName}
                  </div>
                  <div>
                    <span className="font-medium">Date of Birth:</span> {owner2.dateOfBirth}
                  </div>
                  <div>
                    <span className="font-medium">Phone:</span> {owner2.phone}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {owner2.email}
                  </div>
                  <div>
                    <span className="font-medium">Ownership:</span> {owner2.ownershipPercentage}%
                  </div>
                  <div>
                    <span className="font-medium">Address:</span> {formatAddress(owner2.address)}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="space-y-4">
            {/* Disclaimer */}
            <div className="text-sm text-gray-700">
              <p className="mb-4">
                By signing below, I acknowledge that I have read and understand the terms and conditions of this funding
                application. I certify that all information provided is true and accurate to the best of my knowledge. I
                understand that this application does not guarantee funding approval and that additional documentation
                may be required.
              </p>
            </div>

            {/* Signature Method Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">Signature Method</label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="type"
                    checked={signatureMethod === "type"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Type Signature
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="sign"
                    checked={signatureMethod === "sign"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Draw Signature
                </label>
              </div>
            </div>

            {/* Signature Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="signature" className="block text-sm font-medium text-gray-700 mb-1">
                  {signatureMethod === "type" ? "Type Your Signature" : "Draw Your Signature"}
                </label>
                {signatureMethod === "type" ? (
                  <input
                    id="signature"
                    name="signature"
                    type="text"
                    placeholder="Type your full name"
                    value={signature}
                    onChange={(e) => setSignature(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                ) : (
                  <div className="w-full h-24 border border-gray-300 rounded text-sm bg-gray-50 flex items-center justify-center text-gray-500">
                    Signature canvas will be implemented here
                  </div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <div className="p-2 bg-white border border-gray-300 rounded text-sm">{currentDate}</div>
              </div>
            </div>

            {/* Application UUID */}
            <div className="text-xs text-gray-500 mt-4">Application UUID: {appId}</div>

            {/* Sign Button */}
            <div className="flex justify-center mt-6">
              <button
                onClick={handleSignDocument}
                disabled={isProcessingSignature || (signatureMethod === "type" && !signature.trim())}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-8 rounded-sm focus:outline-none focus:shadow-outline"
              >
                {isProcessingSignature ? "Processing..." : "Sign Application"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
